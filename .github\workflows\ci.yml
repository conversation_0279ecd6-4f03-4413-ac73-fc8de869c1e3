name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  release:
    types: [ published ]

env:
  GO_VERSION: '1.19'
  GOLANGCI_LINT_VERSION: 'v1.54'

jobs:
  # Code Quality Checks
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Download dependencies
      run: go mod download

    - name: Verify dependencies
      run: go mod verify

    - name: Check formatting
      run: |
        if [ -n "$(gofmt -l .)" ]; then
          echo "Code is not formatted. Please run 'gofmt -w .'"
          gofmt -l .
          exit 1
        fi

    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: ${{ env.GOLANGCI_LINT_VERSION }}
        args: --timeout=5m

    - name: Run go vet
      run: go vet ./...

    - name: Check for vulnerabilities
      run: |
        go install golang.org/x/vuln/cmd/govulncheck@latest
        govulncheck ./...

  # Unit Tests
  test:
    name: Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        go-version: ['1.19', '1.20', '1.21']
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go ${{ matrix.go-version }}
      uses: actions/setup-go@v4
      with:
        go-version: ${{ matrix.go-version }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ matrix.go-version }}-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-${{ matrix.go-version }}-

    - name: Download dependencies
      run: go mod download

    - name: Run unit tests
      run: go test -v -race -coverprofile=coverage.out ./...

    - name: Generate coverage report
      run: go tool cover -html=coverage.out -o coverage.html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out
        flags: unittests
        name: codecov-umbrella

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: coverage-${{ matrix.go-version }}
        path: |
          coverage.out
          coverage.html

  # Integration Tests
  integration:
    name: Integration Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_DB: testdb
          POSTGRES_USER: testuser
          POSTGRES_PASSWORD: testpass
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-integration-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-integration-

    - name: Download dependencies
      run: go mod download

    - name: Wait for PostgreSQL
      run: |
        until pg_isready -h localhost -p 5432 -U testuser; do
          echo "Waiting for PostgreSQL..."
          sleep 2
        done

    - name: Wait for Redis
      run: |
        until redis-cli -h localhost -p 6379 ping; do
          echo "Waiting for Redis..."
          sleep 2
        done

    - name: Run integration tests
      env:
        POSTGRES_DSN: postgres://testuser:testpass@localhost:5432/testdb?sslmode=disable
        REDIS_DSN: redis://localhost:6379/0
      run: go test -v -tags=integration ./...

  # Performance Tests
  benchmark:
    name: Performance Tests
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-benchmark-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-benchmark-

    - name: Download dependencies
      run: go mod download

    - name: Run benchmarks
      run: go test -bench=. -benchmem -run=^$ ./... | tee benchmark.txt

    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: benchmark.txt

  # Security Scan
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Run Gosec Security Scanner
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: '-fmt sarif -out gosec.sarif ./...'

    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: gosec.sarif

  # Build and Test Docker Image
  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: [quality, test]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: false
        tags: hybridcache/storage:test
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Test Docker image
      run: |
        docker run --rm hybridcache/storage:test --version

  # Release
  release:
    name: Release
    runs-on: ubuntu-latest
    needs: [quality, test, integration, benchmark, security, docker]
    if: github.event_name == 'release'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: |
          ~/.cache/go-build
          ~/go/pkg/mod
        key: ${{ runner.os }}-go-release-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-release-

    - name: Download dependencies
      run: go mod download

    - name: Build binaries
      run: |
        GOOS=linux GOARCH=amd64 go build -o dist/storage-linux-amd64 ./cmd/example
        GOOS=darwin GOARCH=amd64 go build -o dist/storage-darwin-amd64 ./cmd/example
        GOOS=windows GOARCH=amd64 go build -o dist/storage-windows-amd64.exe ./cmd/example

    - name: Create checksums
      run: |
        cd dist
        sha256sum * > checksums.txt

    - name: Upload release assets
      uses: softprops/action-gh-release@v1
      with:
        files: |
          dist/*
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        push: true
        tags: |
          hybridcache/storage:latest
          hybridcache/storage:${{ github.event.release.tag_name }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Notify on failure
  notify:
    name: Notify on Failure
    runs-on: ubuntu-latest
    needs: [quality, test, integration, benchmark, security, docker]
    if: failure()
    steps:
    - name: Notify failure
      run: |
        echo "CI/CD pipeline failed. Please check the logs."
        # Add notification logic here (Slack, email, etc.)
