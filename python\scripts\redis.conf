# Redis configuration for Database Agnostic Storage Library (Python)

# Basic configuration
port 6379
bind 0.0.0.0
protected-mode no

# Memory management
maxmemory 256mb
maxmemory-policy allkeys-lru

# Persistence (disabled for testing)
save ""
appendonly no

# Logging
loglevel notice
logfile ""

# Performance
tcp-keepalive 300
timeout 0

# Security (basic settings for development)
# requirepass testpass

# Database settings
databases 16

# Slow log
slowlog-log-slower-than 10000
slowlog-max-len 128
