# golangci-lint configuration for Database Agnostic Storage Library

run:
  timeout: 5m
  issues-exit-code: 1
  tests: true
  skip-dirs:
    - vendor
    - .git
    - bin
    - dist
  skip-files:
    - ".*\\.pb\\.go$"
    - ".*_generated\\.go$"

output:
  format: colored-line-number
  print-issued-lines: true
  print-linter-name: true
  uniq-by-line: true
  sort-results: true

linters-settings:
  # Cyclomatic complexity
  cyclop:
    max-complexity: 15
    package-average: 10.0
    skip-tests: true

  # Duplicate code detection
  dupl:
    threshold: 100

  # Error handling
  errcheck:
    check-type-assertions: true
    check-blank: true
    exclude-functions:
      - (*database/sql.Rows).Close
      - (*database/sql.Stmt).Close

  # Function length
  funlen:
    lines: 100
    statements: 50

  # Cognitive complexity
  gocognit:
    min-complexity: 20

  # Cyclomatic complexity
  gocyclo:
    min-complexity: 15

  # Dot imports
  goimports:
    local-prefixes: github.com/HybridCache.io/storage

  # Line length
  lll:
    line-length: 120

  # Naming conventions
  revive:
    rules:
      - name: exported
        arguments: [checkPrivate<PERSON><PERSON><PERSON><PERSON>, sayRepetitiveInsteadOfStutters]
      - name: package-comments
      - name: var-naming
      - name: function-result-limit
        arguments: [3]
      - name: argument-limit
        arguments: [5]
      - name: function-length
        arguments: [50, 100]
      - name: max-public-structs
        arguments: [10]

  # Unused code
  unused:
    check-exported: false

  # Whitespace
  wsl:
    strict-append: true
    allow-assign-and-call: true
    allow-multiline-assign: true
    allow-case-trailing-whitespace: true
    allow-cuddle-declarations: false

  # Govet
  govet:
    check-shadowing: true
    enable-all: true
    disable:
      - fieldalignment # Can be too strict for readability

  # Staticcheck
  staticcheck:
    go: "1.19"
    checks: ["all"]

  # Gosec
  gosec:
    severity: medium
    confidence: medium
    excludes:
      - G104 # Audit errors not checked (we handle this with errcheck)

  # Gocritic
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc

linters:
  enable:
    # Enabled by default
    - errcheck
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unused

    # Additional linters
    - asciicheck
    - bidichk
    - bodyclose
    - contextcheck
    - cyclop
    - dupl
    - durationcheck
    - errname
    - errorlint
    - execinquery
    - exhaustive
    - exportloopref
    - forbidigo
    - funlen
    - gochecknoglobals
    - gochecknoinits
    - gocognit
    - goconst
    - gocritic
    - gocyclo
    - godot
    - gofmt
    - gofumpt
    - goheader
    - goimports
    - gomnd
    - gomoddirectives
    - gomodguard
    - goprintffuncname
    - gosec
    - grouper
    - importas
    - lll
    - makezero
    - misspell
    - nakedret
    - nestif
    - nilerr
    - nilnil
    - noctx
    - nolintlint
    - prealloc
    - predeclared
    - promlinter
    - revive
    - rowserrcheck
    - sqlclosecheck
    - stylecheck
    - tenv
    - testpackage
    - thelper
    - tparallel
    - unconvert
    - unparam
    - usestdlibvars
    - wastedassign
    - whitespace
    - wsl

  disable:
    - deadcode    # deprecated
    - exhaustivestruct # deprecated
    - golint      # deprecated
    - ifshort     # deprecated
    - interfacer  # deprecated
    - maligned    # deprecated
    - nosnakecase # deprecated
    - scopelint   # deprecated
    - structcheck # deprecated
    - varcheck    # deprecated

issues:
  exclude-rules:
    # Exclude some linters from running on tests files
    - path: _test\.go
      linters:
        - gocyclo
        - errcheck
        - dupl
        - gosec
        - funlen
        - gocognit
        - cyclop

    # Exclude some staticcheck messages
    - linters:
        - staticcheck
      text: "SA9003:"

    # Exclude lll issues for long lines with go:generate
    - linters:
        - lll
      source: "^//go:generate "

    # Exclude shadow checking on err variables
    - linters:
        - govet
      text: 'shadow: declaration of "err"'

    # Exclude certain gosec rules
    - linters:
        - gosec
      text: "G204:"  # Subprocess launched with variable
      path: _test\.go

    # Exclude magic number detection in tests
    - linters:
        - gomnd
      path: _test\.go

    # Exclude package comment requirement for main packages
    - linters:
        - revive
      text: "package-comments"
      path: cmd/

  exclude-use-default: false
  max-issues-per-linter: 0
  max-same-issues: 0
  new: false

severity:
  default-severity: error
  case-sensitive: false
  rules:
    - linters:
        - dupl
      severity: info
    - linters:
        - gocritic
      severity: warning
