package query

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/AnandSGit/HybridCache.io/pkg/storage"
)

func TestQueryBuilder_Select(t *testing.T) {
	tests := []struct {
		name     string
		fields   []string
		expected string
	}{
		{"single field", []string{"id"}, "SELECT id FROM"},
		{"multiple fields", []string{"id", "name", "email"}, "SELECT id, name, email FROM"},
		{"no fields defaults to *", []string{}, "SELECT * FROM"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			builder := storage.NewBuilder()
			if len(tt.fields) > 0 {
				builder.Select(tt.fields...)
			}
			builder.From("users")

			query, err := builder.Build()
			require.NoError(t, err)
			assert.Contains(t, query.SQL, tt.expected)
		})
	}
}

func TestQueryBuilder_SelectDistinct(t *testing.T) {
	builder := storage.NewBuilder()
	builder.SelectDistinct("category").From("products")

	query, err := builder.Build()
	require.NoError(t, err)
	assert.Contains(t, query.SQL, "SELECT DISTINCT category")
}

func TestQueryBuilder_SelectCount(t *testing.T) {
	tests := []struct {
		name     string
		field    string
		expected string
	}{
		{"count all", "", "SELECT COUNT(*)"},
		{"count specific field", "id", "SELECT COUNT(id)"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			builder := storage.NewBuilder()
			builder.SelectCount(tt.field).From("users")

			query, err := builder.Build()
			require.NoError(t, err)
			assert.Contains(t, query.SQL, tt.expected)
		})
	}
}

func TestQueryBuilder_Where(t *testing.T) {
	builder := storage.NewBuilder()
	builder.Select("*").
		From("users").
		Where(storage.Equal("active", true)).
		Where(storage.GreaterThan("age", 18))

	query, err := builder.Build()
	require.NoError(t, err)

	assert.Contains(t, query.SQL, "WHERE")
	assert.Contains(t, query.SQL, "active = ?")
	assert.Contains(t, query.SQL, "age > ?")
	assert.Equal(t, []interface{}{true, 18}, query.Parameters)
}

func TestQueryBuilder_Join(t *testing.T) {
	builder := storage.NewBuilder()
	builder.Select("u.name", "p.title").
		From("users u").
		InnerJoin("posts p", storage.Equal("p.user_id", "u.id"))

	query, err := builder.Build()
	require.NoError(t, err)

	assert.Contains(t, query.SQL, "INNER JOIN posts p ON p.user_id = ?")
	assert.Equal(t, []interface{}{"u.id"}, query.Parameters)
}

func TestQueryBuilder_OrderBy(t *testing.T) {
	builder := storage.NewBuilder()
	builder.Select("*").
		From("users").
		OrderBy("name", storage.SortDirectionAsc).
		OrderByDesc("created_at")

	query, err := builder.Build()
	require.NoError(t, err)

	assert.Contains(t, query.SQL, "ORDER BY name ASC, created_at DESC")
}

func TestQueryBuilder_LimitOffset(t *testing.T) {
	builder := storage.NewBuilder()
	builder.Select("*").
		From("users").
		Limit(10).
		Offset(20)

	query, err := builder.Build()
	require.NoError(t, err)

	assert.Contains(t, query.SQL, "LIMIT 10")
	assert.Contains(t, query.SQL, "OFFSET 20")
}

func TestQueryBuilder_Page(t *testing.T) {
	builder := storage.NewBuilder()
	builder.Select("*").
		From("users").
		Page(3, 10) // Page 3 with 10 items per page

	query, err := builder.Build()
	require.NoError(t, err)

	assert.Contains(t, query.SQL, "LIMIT 10")
	assert.Contains(t, query.SQL, "OFFSET 20") // (3-1) * 10 = 20
}

func TestQueryBuilder_ComplexQuery(t *testing.T) {
	builder := storage.NewBuilder()
	builder.Select("u.name", "u.email", "COUNT(o.id) as order_count").
		From("users u").
		LeftJoin("orders o", storage.Equal("o.user_id", "u.id")).
		Where(storage.Equal("u.active", true)).
		Where(storage.GreaterThan("u.age", 18)).
		GroupBy("u.id", "u.name", "u.email").
		OrderByDesc("order_count").
		Limit(50)

	query, err := builder.Build()
	require.NoError(t, err)

	expectedParts := []string{
		"SELECT u.name, u.email, COUNT(o.id) as order_count",
		"FROM users u",
		"LEFT JOIN orders o ON o.user_id = ?",
		"WHERE u.active = ? AND u.age > ?",
		"GROUP BY u.id, u.name, u.email",
		"ORDER BY order_count DESC",
		"LIMIT 50",
	}

	for _, part := range expectedParts {
		assert.Contains(t, query.SQL, part)
	}

	assert.Equal(t, []interface{}{"u.id", true, 18}, query.Parameters)
}

func TestConditionHelpers(t *testing.T) {
	tests := []struct {
		name      string
		condition storage.Condition
		field     string
		operator  storage.Operator
		value     interface{}
		values    []interface{}
	}{
		{
			name:      "Equal",
			condition: storage.Equal("id", 1),
			field:     "id",
			operator:  storage.OperatorEqual,
			value:     1,
		},
		{
			name:      "NotEqual",
			condition: storage.NotEqual("status", "deleted"),
			field:     "status",
			operator:  storage.OperatorNotEqual,
			value:     "deleted",
		},
		{
			name:      "GreaterThan",
			condition: storage.GreaterThan("age", 18),
			field:     "age",
			operator:  storage.OperatorGreaterThan,
			value:     18,
		},
		{
			name:      "LessThan",
			condition: storage.LessThan("price", 100.0),
			field:     "price",
			operator:  storage.OperatorLessThan,
			value:     100.0,
		},
		{
			name:      "In",
			condition: storage.In("status", "active", "pending", "completed"),
			field:     "status",
			operator:  storage.OperatorIn,
			values:    []interface{}{"active", "pending", "completed"},
		},
		{
			name:      "Like",
			condition: storage.Like("name", "%john%"),
			field:     "name",
			operator:  storage.OperatorLike,
			value:     "%john%",
		},
		{
			name:      "IsNull",
			condition: storage.IsNull("deleted_at"),
			field:     "deleted_at",
			operator:  storage.OperatorIsNull,
		},
		{
			name:      "IsNotNull",
			condition: storage.IsNotNull("email"),
			field:     "email",
			operator:  storage.OperatorIsNotNull,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.field, tt.condition.Field)
			assert.Equal(t, tt.operator, tt.condition.Operator)
			if tt.value != nil {
				assert.Equal(t, tt.value, tt.condition.Value)
			}
			if tt.values != nil {
				assert.Equal(t, tt.values, tt.condition.Values)
			}
		})
	}
}

func TestCommandBuilder_Insert(t *testing.T) {
	cmd := storage.Insert("users").
		Set("name", "John Doe").
		Set("email", "<EMAIL>").
		Set("age", 30)

	command, err := cmd.Build()
	require.NoError(t, err)

	assert.Equal(t, storage.CommandTypeInsert, command.Type)
	assert.Contains(t, command.SQL, "INSERT INTO users")
	assert.Contains(t, command.SQL, "name")
	assert.Contains(t, command.SQL, "email")
	assert.Contains(t, command.SQL, "age")
	assert.Contains(t, command.SQL, "VALUES")
	assert.Len(t, command.Parameters, 3)
	assert.Contains(t, command.Parameters, "John Doe")
	assert.Contains(t, command.Parameters, "<EMAIL>")
	assert.Contains(t, command.Parameters, 30)
}

func TestCommandBuilder_Update(t *testing.T) {
	cmd := storage.Update("users").
		Set("name", "Jane Doe").
		Set("age", 25).
		Where(storage.Equal("id", 1))

	command, err := cmd.Build()
	require.NoError(t, err)

	assert.Equal(t, storage.CommandTypeUpdate, command.Type)
	assert.Contains(t, command.SQL, "UPDATE users SET")
	assert.Contains(t, command.SQL, "name = ?")
	assert.Contains(t, command.SQL, "age = ?")
	assert.Contains(t, command.SQL, "WHERE id = ?")
	assert.Equal(t, []interface{}{"Jane Doe", 25, 1}, command.Parameters)
}

func TestCommandBuilder_Delete(t *testing.T) {
	cmd := storage.Delete("users").
		Where(storage.Equal("active", false)).
		Where(storage.LessThan("last_login", "2023-01-01"))

	command, err := cmd.Build()
	require.NoError(t, err)

	assert.Equal(t, storage.CommandTypeDelete, command.Type)
	assert.Contains(t, command.SQL, "DELETE FROM users")
	assert.Contains(t, command.SQL, "WHERE active = ? AND last_login < ?")
	assert.Equal(t, []interface{}{false, "2023-01-01"}, command.Parameters)
}

func TestCommandBuilder_Values(t *testing.T) {
	values := map[string]interface{}{
		"name":  "Alice Smith",
		"email": "<EMAIL>",
		"age":   28,
	}

	cmd := storage.Insert("users").Values(values)

	command, err := cmd.Build()
	require.NoError(t, err)

	assert.Equal(t, storage.CommandTypeInsert, command.Type)
	assert.Contains(t, command.SQL, "INSERT INTO users")
	assert.Len(t, command.Parameters, 3)

	// Check that all values are present (order may vary due to map iteration)
	for _, param := range command.Parameters {
		assert.Contains(t, []interface{}{"Alice Smith", "<EMAIL>", 28}, param)
	}
}

func TestCommandBuilder_Errors(t *testing.T) {
	tests := []struct {
		name    string
		builder func() *storage.CommandBuilder
		wantErr bool
	}{
		{
			name: "Insert without table",
			builder: func() *storage.CommandBuilder {
				return storage.NewCommandBuilder().Set("name", "test")
			},
			wantErr: true,
		},
		{
			name: "Insert without values",
			builder: func() *storage.CommandBuilder {
				return storage.Insert("users")
			},
			wantErr: true,
		},
		{
			name: "Update without table",
			builder: func() *storage.CommandBuilder {
				return storage.NewCommandBuilder().Set("name", "test")
			},
			wantErr: true,
		},
		{
			name: "Update without values",
			builder: func() *storage.CommandBuilder {
				return storage.Update("users").Where(storage.Equal("id", 1))
			},
			wantErr: true,
		},
		{
			name: "Delete without table",
			builder: func() *storage.CommandBuilder {
				return storage.NewCommandBuilder().Where(storage.Equal("id", 1))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := tt.builder().Build()
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
